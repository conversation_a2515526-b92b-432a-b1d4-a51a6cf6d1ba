import React, { createContext, useContext, useState } from 'react';

interface MoreDropdownContextType {
  isVisible: boolean;
  showDropdown: () => void;
  hideDropdown: () => void;
}

const MoreDropdownContext = createContext<MoreDropdownContextType | undefined>(undefined);

export function MoreDropdownProvider({ children }: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);

  const showDropdown = () => {
    setIsVisible(true);
  };

  const hideDropdown = () => {
    setIsVisible(false);
  };

  const value = {
    isVisible,
    showDropdown,
    hideDropdown,
  };

  return (
    <MoreDropdownContext.Provider value={value}>
      {children}
    </MoreDropdownContext.Provider>
  );
}

export function useMoreDropdown() {
  const context = useContext(MoreDropdownContext);
  if (context === undefined) {
    throw new Error('useMoreDropdown must be used within a MoreDropdownProvider');
  }
  return context;
}
