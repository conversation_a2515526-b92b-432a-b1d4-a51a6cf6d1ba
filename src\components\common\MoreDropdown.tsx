import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  Modal,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useMoreDropdown } from '../../contexts/MoreDropdownContext';
import { useAppStore } from '../../stores';
import { useResolvedTheme } from '../providers/ThemeProvider';

const { height: screenHeight } = Dimensions.get('window');

interface MoreMenuItem {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  route?: string;
  comingSoon?: boolean;
}

const moreMenuItems: MoreMenuItem[] = [
  {
    id: 'events',
    title: 'Events',
    description: 'Countdown to birthdays, anniversaries & holidays',
    icon: 'calendar-outline',
    route: '/events',
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Track your progress and insights',
    icon: 'analytics-outline',
    comingSoon: true,
  },
  {
    id: 'breathing',
    title: 'Breathing Exercises',
    description: 'Guided breathing and mindfulness',
    icon: 'leaf-outline',
    comingSoon: true,
  },
  {
    id: 'goals',
    title: 'Goals & Milestones',
    description: 'Set and track long-term objectives',
    icon: 'trophy-outline',
    comingSoon: true,
  },
  {
    id: 'backup',
    title: 'Backup & Sync',
    description: 'Secure your data across devices',
    icon: 'cloud-outline',
    comingSoon: true,
  },
];

export default function MoreDropdown() {
  const { isVisible, hideDropdown } = useMoreDropdown();
  const { setActiveTab } = useAppStore();
  const insets = useSafeAreaInsets();
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const resolvedTheme = useResolvedTheme();

  useEffect(() => {
    if (isVisible) {
      // Animate dropdown in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate dropdown out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, opacityAnim, slideAnim]);

  const handleMenuItemPress = (item: MoreMenuItem) => {
    hideDropdown();

    if (item.comingSoon) {
      router.push('/coming-soon');
    } else if (item.route) {
      router.push(item.route as any);
    }
  };

  const closeDropdown = () => {
    hideDropdown();
    // Navigate back to Home tab when closing the dropdown
    setActiveTab('home');
    router.push('/(tabs)');
  };

  return (
    <Modal visible={isVisible} transparent animationType="none" onRequestClose={closeDropdown}>
      <TouchableWithoutFeedback onPress={closeDropdown}>
        <View className="flex-1">
          <Animated.View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              opacity: opacityAnim,
            }}
          />
          <Animated.View
            style={{
              position: 'absolute',
              bottom: insets.bottom,
              left: 0,
              right: 0,
              transform: [{ translateY: slideAnim }],
            }}
            className="rounded-t-3xl bg-gray-800 shadow-2xl"
          >
            <View className="p-6">
              {/* Handle */}
              <View className="mb-6 h-1 w-12 self-center rounded-full bg-gray-600" />

              {/* Menu Items */}
              <View className="space-y-2">
                {moreMenuItems.map(item => (
                  <TouchableOpacity
                    key={item.id}
                    onPress={() => handleMenuItemPress(item)}
                    className="flex-row items-center rounded-xl bg-gray-700 p-4"
                  >
                    <View className="mr-4 h-12 w-12 items-center justify-center rounded-full bg-blue-900">
                      <Ionicons
                        name={item.icon}
                        size={24}
                        color="#3B82F6"
                      />
                    </View>
                    <View className="flex-1">
                      <View className="flex-row items-center">
                        <Text className="font-semibold text-gray-100">
                          {item.title}
                        </Text>
                        {item.comingSoon && (
                          <View className="ml-2 rounded-full bg-orange-900 px-2 py-1">
                            <Text className="text-xs font-medium text-orange-400">
                              Coming Soon
                            </Text>
                          </View>
                        )}
                      </View>
                      <Text className="mt-1 text-sm text-gray-400">
                        {item.description}
                      </Text>
                    </View>
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color="#6B7280"
                    />
                  </TouchableOpacity>
                ))}
              </View>

              {/* Close Button */}
              <TouchableOpacity
                onPress={closeDropdown}
                className="mt-6 rounded-xl bg-gray-700 py-4"
              >
                <Text className="text-center font-semibold text-gray-100">
                  Close
                </Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}
