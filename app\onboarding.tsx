import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppStore } from '../src/stores';

export default function OnboardingScreen() {
  const { setFirstLaunch, setOnboardingCompleted } = useAppStore();

  const handleGetStarted = () => {
    setFirstLaunch(false);
    setOnboardingCompleted(true);
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-900" edges={['top', 'bottom']}>
      <StatusBar style="light" />
      <View className="flex-1">
        {/* Content */}
        <View className="flex-1 items-center justify-center px-8">
          {/* Logo/Icon */}
          <View className="mb-8 h-32 w-32 items-center justify-center rounded-full bg-blue-500">
            <Ionicons name="checkmark-circle" size={64} color="white" />
          </View>

          {/* Title */}
          <Text className="mb-4 text-center text-3xl font-bold text-gray-100">Welcome to WinArc</Text>

          {/* Subtitle */}
          <Text className="mb-12 text-center text-lg leading-6 text-gray-400">
            Build lasting habits, track your progress, and achieve your goals with our powerful habit
            tracker.
          </Text>

          {/* Features */}
          <View className="mb-12 w-full space-y-4">
            <View className="flex-row items-center">
              <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-green-900/20">
                <Ionicons name="checkmark" size={20} color="#10B981" />
              </View>
              <Text className="flex-1 text-gray-300">Track daily habits and build streaks</Text>
            </View>

            <View className="flex-row items-center">
              <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-purple-900/20">
                <Ionicons name="timer" size={20} color="#8B5CF6" />
              </View>
              <Text className="flex-1 text-gray-300">Focus with Pomodoro timer</Text>
            </View>

            <View className="flex-row items-center">
              <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-orange-900/20">
                <Ionicons name="stats-chart" size={20} color="#F59E0B" />
              </View>
              <Text className="flex-1 text-gray-300">Visualize your progress with insights</Text>
            </View>

            <View className="flex-row items-center">
              <View className="mr-4 h-10 w-10 items-center justify-center rounded-full bg-blue-900/20">
                <Ionicons name="heart" size={20} color="#3B82F6" />
              </View>
              <Text className="flex-1 text-gray-300">Practice mindfulness and breathing</Text>
            </View>
          </View>
        </View>

        {/* Get Started Button */}
        <View className="px-8 pb-12">
          <TouchableOpacity
            onPress={handleGetStarted}
            className="items-center rounded-xl bg-blue-500 py-4 shadow-sm"
          >
            <Text className="text-lg font-semibold text-white">Get Started</Text>
          </TouchableOpacity>

          <Text className="mt-4 text-center text-sm text-gray-500">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}
