import { Stack } from 'expo-router';
import { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import 'reflect-metadata';
import { ThemeProvider, useResolvedTheme } from '../src/components';
import ErrorBoundary from '../src/components/common/ErrorBoundary';
import { databaseService } from '../src/database';
import '../src/styles/global.css';

export default function RootLayout() {
  useEffect(() => {
    const initializeApp = async () => {
      try {
        await databaseService.initialize();
        await databaseService.createDefaultUser();
      } catch {
        // Silently handle initialization errors
      }
    };

    initializeApp();
  }, []);

  return (
    <SafeAreaProvider>
      <ErrorBoundary>
        <ThemeProvider>
          <RootNavigator />
        </ThemeProvider>
      </ErrorBoundary>
    </SafeAreaProvider>
  );
}

function RootNavigator() {
  const resolvedTheme = useResolvedTheme();

  const backgroundColor = resolvedTheme === 'dark' ? '#111827' : '#F9FAFB';

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor },
        animation: 'fade',
        animationDuration: 200,
      }}
    >
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
          contentStyle: { backgroundColor },
        }}
      />
      <Stack.Screen
        name="onboarding"
        options={{
          headerShown: false,
          contentStyle: { backgroundColor },
        }}
      />
      <Stack.Screen
        name="habit-form"
        options={{
          headerShown: false,
          presentation: 'modal',
          contentStyle: { backgroundColor },
          animation: 'slide_from_bottom',
          animationDuration: 300,
        }}
      />
      <Stack.Screen
        name="habit-detail"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 250,
          contentStyle: { backgroundColor },
        }}
      />
    </Stack>
  );
}
