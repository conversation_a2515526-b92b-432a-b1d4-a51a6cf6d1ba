import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useRef, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAppStore, useTimerStore } from '../../src/stores';

export default function PomodoroScreen() {
  const {
    status,
    timeRemaining,
    totalTime,
    pomodoroPhase,
    pomodoroSession,
    pauseTimer,
    resumeTimer,
    resetTimer,
    tick,
    startPomodoro,
    setTimerType,
  } = useTimerStore();

  const { setActiveTab, pomodoroWorkDuration, pomodoroBreakDuration } = useAppStore();
  const intervalRef = useRef<number | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    setActiveTab('pomodoro');
    // Set timer type to pomodoro by default
    setTimerType('pomodoro');
  }, [setActiveTab, setTimerType]);

  useEffect(() => {
    if (status === 'running') {
      intervalRef.current = setInterval(() => {
        tick();
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, tick]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    if (totalTime === 0) return 0;
    // Since we only have 'pomodoro' type, calculate progress normally
    return (totalTime - timeRemaining) / totalTime;
  };

  const getPhaseColor = () => {
    switch (pomodoroPhase) {
      case 'work':
        return '#EF4444'; // red
      case 'break':
        return '#10B981'; // green
      case 'longBreak':
        return '#3B82F6'; // blue
      default:
        return '#6B7280'; // gray
    }
  };

  const getPhaseText = () => {
    switch (pomodoroPhase) {
      case 'work':
        return 'Focus Time';
      case 'break':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return 'Timer';
    }
  };

  return (
    <ScrollView className="flex-1 bg-gray-900">
      <StatusBar style="light" />

      {/* Header */}
      <View className="bg-gray-800 px-6 pb-6 pt-16 shadow-sm">
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className="text-2xl font-bold text-gray-100">Pomodoro</Text>
            <Text className="mt-1 text-gray-400">
              Focus with the Pomodoro Technique
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowSettings(!showSettings)}
            className="h-10 w-10 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
          >
            <Ionicons name="settings-outline" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Timer Display */}
      <View className="items-center justify-center px-6 py-8">
        <View className="mb-8 items-center">
          {/* Circular Progress */}
          <View className="relative h-80 w-80 items-center justify-center">
            <View
              className="absolute h-full w-full rounded-full border-8"
              style={{
                borderColor: getPhaseColor(),
                opacity: 0.2,
              }}
            />
            <View
              className="absolute h-full w-full rounded-full border-8 border-transparent"
              style={{
                borderTopColor: getPhaseColor(),
                transform: [{ rotate: `${getProgress() * 360}deg` }],
              }}
            />

            {/* Time Display */}
            <View className="items-center">
              <Text className="mb-2 text-6xl font-light text-gray-900 dark:text-gray-100">
                {formatTime(timeRemaining)}
              </Text>
              <Text className="text-lg font-medium" style={{ color: getPhaseColor() }}>
                {getPhaseText()}
              </Text>
              <Text className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Session {pomodoroSession}
              </Text>
            </View>
          </View>
        </View>

        {/* Control Buttons */}
        <View className="mt-8 flex-row items-center justify-center">
          <TouchableOpacity
            onPress={resetTimer}
            className="mr-6 h-16 w-16 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700"
          >
            <Ionicons name="refresh" size={24} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (status === 'running') {
                pauseTimer();
              } else if (status === 'paused') {
                resumeTimer();
              } else {
                startPomodoro();
              }
            }}
            className="mx-6 h-20 w-20 items-center justify-center rounded-full"
            style={{
              backgroundColor: getPhaseColor(),
            }}
          >
            <Ionicons name={status === 'running' ? 'pause' : 'play'} size={32} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (status === 'running' || status === 'paused') {
                resetTimer();
              }
            }}
            className="ml-6 h-16 w-16 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700"
          >
            <Ionicons name="play-skip-forward" size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Pomodoro Stats */}
        <View className="mx-6 mt-8 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <Text className="mb-4 text-center text-lg font-semibold text-gray-900 dark:text-gray-100">
            Current Session
          </Text>
          <View className="flex-row justify-around">
            <View className="items-center">
              <Text className="text-2xl font-bold" style={{ color: getPhaseColor() }}>
                {pomodoroSession}
              </Text>
              <Text className="text-sm text-gray-600 dark:text-gray-400">Session</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-green-500">{pomodoroWorkDuration}</Text>
              <Text className="text-sm text-gray-600 dark:text-gray-400">Work (min)</Text>
            </View>
            <View className="items-center">
              <Text className="text-2xl font-bold text-blue-500">{pomodoroBreakDuration}</Text>
              <Text className="text-sm text-gray-600 dark:text-gray-400">Break (min)</Text>
            </View>
          </View>
        </View>

        {/* Phase Description */}
        <View className="mx-6 mt-6 rounded-xl bg-white p-6 shadow-sm dark:bg-gray-800">
          <Text className="mb-2 text-center text-lg font-semibold text-gray-900 dark:text-gray-100">
            {getPhaseText()}
          </Text>
          <Text className="text-center text-sm text-gray-600 dark:text-gray-400">
            {pomodoroPhase === 'work'
              ? 'Focus on your task without distractions. Stay committed to your goal.'
              : pomodoroPhase === 'break'
                ? 'Take a short break. Stretch, hydrate, or relax for a few minutes.'
                : 'Enjoy a longer break. Step away from your workspace and recharge.'}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}
