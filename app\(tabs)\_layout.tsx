import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useResolvedTheme } from '../../src/components';
import MoreDropdown from '../../src/components/common/MoreDropdown';
import { MoreDropdownProvider, useMoreDropdown } from '../../src/contexts/MoreDropdownContext';

function TabLayoutContent() {
  const resolvedTheme = useResolvedTheme();
  const insets = useSafeAreaInsets();
  const { showDropdown } = useMoreDropdown();

  const backgroundColor = resolvedTheme === 'dark' ? '#111827' : '#F9FAFB';

  // Reduced bottom margin for a more subtle floating effect
  const floatingNavBottom = insets.bottom + 12;

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#9CA3AF',
        tabBarStyle: {
          // Floating navigation bar design
          position: 'absolute',
          backgroundColor: '#1F2937',
          borderTopWidth: 0,
          borderRadius: 25,
          height: 70, // Slightly reduced height
          paddingBottom: 10,
          paddingTop: 10,
          paddingHorizontal: 16,
          // Better floating positioning with increased side margins
          bottom: floatingNavBottom,
          left: 16, // Increased from 20 to create more pronounced floating effect
          right: 16, // Increased from 20 to create more pronounced floating effect
          marginHorizontal: 8, // Additional margin for more floating appearance
          // Enhanced shadow for floating effect
          shadowColor: '#000000',
          shadowOffset: {
            width: 0,
            height: 6,
          },
          shadowOpacity: 0.3,
          shadowRadius: 12,
          elevation: 12,
          // Ensure proper spacing between tabs
          justifyContent: 'space-around',
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '600',
          marginTop: 3,
        },
        tabBarIconStyle: {
          marginBottom: 1,
        },
        // Hide the header titles completely
        headerShown: false,
        sceneStyle: {
          backgroundColor,
          // Reduced padding to prevent excessive bottom spacing
          paddingBottom: floatingNavBottom + 70 + 8, // nav bottom + nav height + small buffer
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "home" : "home-outline"}
              size={focused ? size + 2 : size}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="habits"
        options={{
          title: 'Habit',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "checkmark-circle" : "checkmark-circle-outline"}
              size={focused ? size + 2 : size}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="more"
        options={{
          title: 'More',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "ellipsis-horizontal" : "ellipsis-horizontal"}
              size={focused ? size + 2 : size}
              color={color}
            />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            // Prevent default navigation
            e.preventDefault();
            // Show dropdown instead
            showDropdown();
          },
        }}
      />
      <Tabs.Screen
        name="pomodoro"
        options={{
          title: 'Pomodoro',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "timer" : "timer-outline"}
              size={focused ? size + 2 : size}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? "person" : "person-outline"}
              size={focused ? size + 2 : size}
              color={color}
            />
          ),
        }}
      />
    </Tabs>
  );
}

export default function TabLayout() {
  return (
    <MoreDropdownProvider>
      <TabLayoutContent />
      <MoreDropdown />
    </MoreDropdownProvider>
  );
}