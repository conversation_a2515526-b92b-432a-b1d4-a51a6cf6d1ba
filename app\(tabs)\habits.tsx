import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { HabitBulkActions, HabitList, HabitReorderList } from '../../src/components/habits';
import { Habit } from '../../src/database/entities/Habit';
import { useHabits } from '../../src/hooks/useHabits';
import { useAppStore } from '../../src/stores';

export default function HabitsScreen() {
  const router = useRouter();
  const { setActiveTab } = useAppStore();
  const {
    activeHabits,
    isLoading,
    toggleHabitCompletion,
    deleteHabits,
    archiveHabits,
    reorderHabits,
  } = useHabits();

  // Selection mode state
  const [selectionMode, setSelectionMode] = React.useState(false);
  const [selectedHabits, setSelectedHabits] = React.useState<Habit[]>([]);
  const [showReorderModal, setShowReorderModal] = React.useState(false);

  useEffect(() => {
    setActiveTab('habits');
  }, [setActiveTab]);

  const handleCreateHabit = () => {
    router.push('/habit-form');
  };

  const handleHabitPress = (habit: Habit) => {
    router.push(`/habit-detail?habitId=${habit.id}`);
  };

  const handleHabitLongPress = (habit: Habit) => {
    // Enter selection mode and select the habit
    setSelectionMode(true);
    setSelectedHabits([habit]);
  };

  // Selection mode handlers
  const handleToggleSelection = (habit: Habit) => {
    setSelectedHabits(prev => {
      const isSelected = prev.some(h => h.id === habit.id);
      if (isSelected) {
        return prev.filter(h => h.id !== habit.id);
      } else {
        return [...prev, habit];
      }
    });
  };

  const handleSelectAll = () => {
    setSelectedHabits(activeHabits);
  };

  const handleClearSelection = () => {
    setSelectedHabits([]);
    setSelectionMode(false);
  };

  const handleDeleteSelected = async (habitIds: string[]) => {
    await deleteHabits(habitIds);
  };

  const handleArchiveSelected = async (habitIds: string[]) => {
    await archiveHabits(habitIds);
  };

  const handleReorderHabits = async (habitIds: string[]) => {
    await reorderHabits(habitIds);
    setShowReorderModal(false);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-900" edges={['top']}>
      <StatusBar style="light" />
      {/* Header */}
      <View className="bg-gray-800 px-6 pb-6 pt-6 shadow-sm">
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-2xl font-bold text-gray-100">My Habits</Text>
            <Text className="mt-1 text-gray-400">
              {activeHabits.length} active habit{activeHabits.length !== 1 ? 's' : ''}
            </Text>
          </View>
          <View className="flex-row space-x-3">
            {/* Reorder button */}
            {activeHabits.length > 1 && !selectionMode && (
              <TouchableOpacity
                onPress={() => setShowReorderModal(true)}
                className="h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
              >
                <Ionicons name="reorder-three" size={24} color="#6B7280" />
              </TouchableOpacity>
            )}

            {/* Selection mode toggle */}
            {activeHabits.length > 0 && !selectionMode && (
              <TouchableOpacity
                onPress={() => setSelectionMode(true)}
                className="h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
              >
                <Ionicons name="checkmark-circle-outline" size={24} color="#6B7280" />
              </TouchableOpacity>
            )}

            {/* Exit selection mode */}
            {selectionMode && (
              <TouchableOpacity
                onPress={handleClearSelection}
                className="h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
              >
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            )}

            {/* Create habit button */}
            <TouchableOpacity
              onPress={handleCreateHabit}
              className="h-12 w-12 items-center justify-center rounded-full bg-blue-500"
            >
              <Ionicons name="add" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Habit List */}
      <View className="flex-1 px-6 pt-6">
        <HabitList
          habits={activeHabits}
          onToggleCompletion={toggleHabitCompletion}
          onHabitPress={selectionMode ? undefined : handleHabitPress}
          onHabitLongPress={handleHabitLongPress}
          onCreateHabit={handleCreateHabit}
          isLoading={isLoading}
          showProgress={true}
          emptyStateTitle="No habits yet"
          emptyStateDescription="Start building your routine by creating your first habit"
          emptyStateIcon="leaf-outline"
          selectionMode={selectionMode}
          selectedHabits={selectedHabits}
          onToggleSelection={handleToggleSelection}
        />
      </View>

      {/* Bulk Actions */}
      <HabitBulkActions
        selectedHabits={selectedHabits}
        onDeleteSelected={handleDeleteSelected}
        onArchiveSelected={handleArchiveSelected}
        onClearSelection={handleClearSelection}
        onSelectAll={handleSelectAll}
        totalHabits={activeHabits.length}
        isVisible={selectionMode}
      />

      {/* Reorder Modal */}
      <Modal visible={showReorderModal} animationType="slide" presentationStyle="pageSheet">
        <HabitReorderList
          habits={activeHabits}
          onReorder={handleReorderHabits}
          onClose={() => setShowReorderModal(false)}
          isVisible={showReorderModal}
        />
      </Modal>
    </SafeAreaView>
  );
}
