{"expo": {"name": "WinArc", "slug": "WinArc", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "winarc", "userInterfaceStyle": "dark", "newArchEnabled": true, "ios": {"supportsTablet": true, "statusBarStyle": "light"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "statusBar": {"barStyle": "light-content", "backgroundColor": "#000000"}, "navigationBar": {"backgroundColor": "#000000"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}